org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Force compile SDK version
android.compileSdk=35
android.targetSdk=35

# 📦 APK Size Optimization Properties
android.enableR8.fullMode=true
android.enableR8=true
android.enableDexingArtifactTransform=true
android.enableDexingArtifactTransform.desugaring=true

# 🚀 Build Performance and Size Optimizations
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
android.enableBuildCache=true

# 📱 Flutter-specific optimizations
flutter.compilationTraceEnabled=true
flutter.enableImpeller=true
